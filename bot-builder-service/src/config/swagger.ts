/**
 * Swagger Configuration for Bot Builder Service
 *
 * OpenAPI 3.0 specification for all administrative APIs
 */

import swaggerJsdoc from "swagger-jsdoc";
import { SwaggerDefinition } from "swagger-jsdoc";
import { OpenApiGeneratorV3 } from "@asteasolutions/zod-to-openapi";

import {
  AppIdParamSchema,
  CreateAppSchema,
  UpdateAppSchema,
  GetAppsQuerySchema,
  CreateBotRequestSchema,
  UpdateBotRequestSchema,
  BotIdParamSchema,
  BotChannelParamSchema,
  CreateChannelIntegrationSchema,
  BotChannelIdParamSchema,
  UpdateChannelIntegrationSchema,
  CreateFlowRequestSchema,
  FlowIdParamSchema,
  UpdateFlowRequestSchema,
  FlowAppIdParamSchema,
  GetFlowsQuerySchema,
  BulkCreateFlowsRequestSchema,
  CreateFaqCategorySchema,
  UpdateFaqCategorySchema,
  CreateFaqItemSchema,
  UpdateFaqItemSchema,
  CreateFaqTranslationSchema,
  UpdateFaqTranslationSchema,
  CreateIntentItemSchema,
  UpdateIntentItemSchema,
  CreateIntentUtteranceSchema,
  UpdateIntentUtteranceSchema,
  CreateIntentUtteranceTranslationSchema,
  UpdateIntentUtteranceTranslationSchema,
  CreateEntitySchema,
  UpdateEntitySchema,
  CreateLanguageSchema,
  UpdateLanguageSchema,
  CreateBotLanguageSchema,
  UpdateBotLanguageSchema,
} from "../schemas";

const openApiGenerator = new OpenApiGeneratorV3([
  {
    schema: CreateAppSchema,
    type: "schema",
  },
  {
    schema: UpdateAppSchema,
    type: "schema",
  },
  {
    schema: AppIdParamSchema,
    type: "schema",
  },
  {
    schema: GetAppsQuerySchema,
    type: "schema",
  },
  {
    schema: CreateBotRequestSchema,
    type: "schema",
  },
  {
    schema: UpdateBotRequestSchema,
    type: "schema",
  },
  {
    schema: BotIdParamSchema,
    type: "schema",
  },
  {
    schema: BotChannelParamSchema,
    type: "schema",
  },
  {
    schema: CreateChannelIntegrationSchema,
    type: "schema",
  },
  {
    schema: BotChannelIdParamSchema,
    type: "schema",
  },
  {
    schema: UpdateChannelIntegrationSchema,
    type: "schema",
  },
  {
    schema: CreateFlowRequestSchema,
    type: "schema",
  },
  {
    schema: FlowIdParamSchema,
    type: "schema",
  },
  {
    schema: UpdateFlowRequestSchema,
    type: "schema",
  },
  {
    schema: FlowAppIdParamSchema,
    type: "schema",
  },
  {
    schema: GetFlowsQuerySchema,
    type: "schema",
  },
  {
    schema: BulkCreateFlowsRequestSchema,
    type: "schema",
  },
  {
    schema: CreateFaqCategorySchema,
    type: "schema",
  },
  {
    schema: UpdateFaqCategorySchema,
    type: "schema",
  },
  {
    schema: CreateFaqItemSchema,
    type: "schema",
  },
  {
    schema: UpdateFaqItemSchema,
    type: "schema",
  },
  {
    schema: CreateFaqTranslationSchema,
    type: "schema",
  },
  {
    schema: UpdateFaqTranslationSchema,
    type: "schema",
  },
  {
    schema: CreateIntentItemSchema,
    type: "schema",
  },
  {
    schema: UpdateIntentItemSchema,
    type: "schema",
  },
  {
    schema: CreateIntentUtteranceSchema,
    type: "schema",
  },
  {
    schema: UpdateIntentUtteranceSchema,
    type: "schema",
  },
  {
    schema: CreateIntentUtteranceTranslationSchema,
    type: "schema",
  },
  {
    schema: UpdateIntentUtteranceTranslationSchema,
    type: "schema",
  },
  {
    schema: CreateEntitySchema,
    type: "schema",
  },
  {
    schema: UpdateEntitySchema,
    type: "schema",
  },
  {
    schema: CreateLanguageSchema,
    type: "schema",
  },
  {
    schema: UpdateLanguageSchema,
    type: "schema",
  },
  {
    schema: CreateBotLanguageSchema,
    type: "schema",
  },
  {
    schema: UpdateBotLanguageSchema,
    type: "schema",
  },
]);

const swaggerDefinition: SwaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "Bot Builder Service API",
    version: "1.0.0",
    description:
      "Administrative API for managing bots, flows, and configurations in the no-code chatbot platform",
    contact: {
      name: "Chatbot Platform Team",
      email: "<EMAIL>",
    },
    license: {
      name: "MIT",
      url: "https://opensource.org/licenses/MIT",
    },
  },
  servers: [
    {
      url: "http://localhost:3000",
      description: "Development server",
    },
    {
      url: "https://api.chatbot-platform.com",
      description: "Production server",
    },
  ],
  components: {
    securitySchemes: {
      BearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for user authentication",
      },
    },
    ...openApiGenerator.generateComponents(),
  },
  tags: [
    {
      name: "Bots",
      description: "Bot management operations",
    },
    {
      name: "Flows",
      description: "Flow management operations",
    },
    {
      name: "Health",
      description: "Health check endpoints",
    },
    {
      name: "FAQ Categories",
      description: "FAQ category management operations",
    },
    {
      name: "FAQ Items",
      description: "FAQ item management operations",
    },
    {
      name: "FAQ Translations",
      description: "FAQ translation management operations",
    },
    {
      name: "Intent Items",
      description: "Intent item management operations",
    },
    {
      name: "Intent Utterances",
      description: "Intent utterance management operations",
    },
    {
      name: "Intent Utterance Translations",
      description: "Intent utterance translation management operations",
    },
    {
      name: "Entities",
      description: "Entity management operations",
    },
    {
      name: "Languages",
      description: "Language management operations",
    },
    {
      name: "Bot Languages",
      description: "Bot language configuration operations",
    },
  ],
};

const options = {
  definition: swaggerDefinition,
  apis: [
    "./src/controllers/*.ts",
    "./src/routers/*.ts",
  ],
  swaggerOptions: {
    persistAuthorization: true,
  },
};

export const swaggerSpec = swaggerJsdoc(options);
export default swaggerSpec;
