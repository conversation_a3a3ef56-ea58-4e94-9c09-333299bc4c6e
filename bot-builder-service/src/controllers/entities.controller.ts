import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  parseIncludeQuery,
  successResponse,
  errorResponse,
  IncludeQuery,
} from "@neuratalk/common";
import { CreateEntityRequest, UpdateEntityRequest } from "../schemas";
import { logger } from "@neuratalk/common";

import { AppContext } from "../types/context.types";

export class EntitiesController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/entities:
   *   post:
   *     summary: Create entity
   *     tags: [Entities]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateEntitySchema'
   *     responses:
   *       201:
   *         description: Entity created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/EntitySchema'
   *       400:
   *         description: Validation error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public create = async (
    req: Request<any, any, CreateEntityRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const entity = await this.models.Entities.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Entity created: ${entity.id}`);
      res.status(201).json(successResponse(entity));
    } catch (error) {
      logger.error("Error creating entity:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/entities:
   *   get:
   *     summary: List entities with filtering and pagination
   *     tags: [Entities]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         example: '{"name":{"like":"city"}}'
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem)
   *     responses:
   *       200:
   *         description: List of entities with pagination
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/PaginatedEntitiesSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const result = await getPaginatedResults(
        this.models.Entities,
        req.query,
        ["name"],
        includeAssociations,
      );

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching entities:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch entities" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   get:
   *     summary: Get entity by ID
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem)
   *     responses:
   *       200:
   *         description: Entity retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/EntitySchema'
   *       404:
   *         description: Entity not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public getById = async (
    req: Request<UuidParams, any, any, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const entity = await this.models.Entities.findOne({
        where: { id },
        include: includeAssociations,
      });

      if (!entity) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Entity not found" }));
        return;
      }

      res.json(successResponse(entity));
    } catch (error) {
      logger.error(`Error fetching entity ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch entity" }));
    }
  };

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   put:
   *     summary: Update entity
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., intentItem)
   *     responses:
   *       200:
   *         description: Updated
   */
  public update = async (
    req: Request<UuidParams, any, UpdateEntityRequest, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.Entities.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Entity not found" }));
        return;
      }

      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const entity = await this.models.Entities.findByPk(id, {
        include: includeAssociations,
      });
      logger.info(`Entity updated: ${id}`);

      res.json(successResponse(entity));
    } catch (error) {
      logger.error(`Error updating entity ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/entities/{id}:
   *   delete:
   *     summary: Delete entity
   *     tags: [Entities]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Deleted
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.Entities.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "Entity not found" }));
        return;
      }

      logger.info(`Entity deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting entity ${req.params.id}:`, error);
      res
        .status(500)
        .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete entity" }));
    }
  };
}
