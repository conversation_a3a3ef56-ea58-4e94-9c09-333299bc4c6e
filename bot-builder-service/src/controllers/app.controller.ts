import { Request, Response } from "express";
import { getStudioAppsService } from "api_gw";
import { logger, successResponse, errorResponse } from "@neuratalk/common";
import { CreateAppRequest, UpdateAppRequest, AppIdParam, GetAppsQuery } from "../schemas";

/**
 * Controller for handling application management.
 * Proxies requests to the studio-apps-service, which contains the core business logic.
 *
 * @swagger
 * tags:
 *   name: Apps
 *   description: Application management operations
 * components:
 *   schemas:
 *     App:
 *       type: object
 *       properties:
 *         id: { type: string, format: uuid }
 *         name: { type: string }
 *         desc: { type: string }
 *         status: { type: integer, description: "0: Draft, 1: Submitted, 2: Approved for Staging, etc." }
 *         appData: { type: object, description: "JSON object representing the app's workflow and modules." }
 *         owner: { type: integer }
 *         createdBy: { type: integer }
 *         modifiedBy: { type: integer }
 *         createdAt: { type: string, format: "date-time" }
 *         updatedAt: { type: string, format: "date-time" }
 *     CreateAppRequest:
 *       type: object
 *       required: [name]
 *       properties:
 *         name: { type: string, maxLength: 32, example: "My New App" }
 *         desc: { type: string, maxLength: 128, example: "This is a sample app." }
 *         appData: { type: object }
 *     UpdateAppRequest:
 *       type: object
 *       properties:
 *         name: { type: string, maxLength: 32 }
 *         desc: { type: string, maxLength: 128 }
 *         appData: { type: object }
 */
export class AppController {
  private studioAppsService;

  constructor() {
    this.studioAppsService = getStudioAppsService();
  }

  /**
   * @swagger
   * /api/v1/apps/{appId}/update:
   *   put:
   *     summary: Update an existing app
   *     tags: [Apps]
   *     parameters:
   *       - in: path
   *         name: appId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: The ID of the app to update.
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateAppRequest'
   *     responses:
   *       200:
   *         description: App updated successfully.
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error.
   */
  public updateApp = async (
    req: Request<AppIdParam, any, UpdateAppRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const app = await this.studioAppsService.updateAppInfo(req, res);
      if (res.headersSent) return;

      res.status(200).json(successResponse(app));
    } catch (error) {
      logger.error("Error in updateApp controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res
          .status(500)
          .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to update app" }));
      }
    }
  };

  /**
   * @swagger
   * /api/v1/apps:
   *   post:
   *     summary: Create a new app
   *     tags: [Apps]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateAppRequest'
   *     responses:
   *       201:
   *         description: App created successfully.
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error.
   */
  public createApp = async (
    req: Request<{}, any, CreateAppRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const app = await this.studioAppsService.createAppInfo(req, res);
      if (res.headersSent) return;

      res.status(201).json(successResponse(app));
    } catch (error) {
      logger.error("Error in createApp controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res
          .status(500)
          .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to create app" }));
      }
    }
  };

  /**
   * @swagger
   * /api/v1/apps:
   *   get:
   *     summary: Get a list of apps
   *     tags: [Apps]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: search
   *         schema: { type: string }
   *       - in: query
   *         name: status
   *         schema: { type: string }
   *     responses:
   *       200:
   *         description: A list of apps.
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       500:
   *         description: Internal server error.
   */
  public getApps = async (req: Request, res: Response): Promise<void> => {
    try {
      const apps = await this.studioAppsService.getAllApps(req, res);
      if (res.headersSent) return;

      res.status(200).json(successResponse(apps));
    } catch (error) {
      logger.error("Error in getApps controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res
          .status(500)
          .json(errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to get apps" }));
      }
    }
  };

  /**
   * @swagger
   * /api/v1/apps/{appId}:
   *   get:
   *     summary: Get an app by ID
   *     tags: [Apps]
   *     parameters:
   *       - in: path
   *         name: appId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: The ID of the app to retrieve.
   *     responses:
   *       200:
   *         description: App retrieved successfully.
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   *       404:
   *         description: App not found.
   */

  public getAppById = async (req: Request<AppIdParam, any, any>, res: Response): Promise<void> => {
    try {
      const app = await this.studioAppsService.getAppInfo(req, res);
      if (app === null || !app) {
        res.status(404).json(
          errorResponse({
            code: "NOT_FOUND",
            message: `App with ID ${req.params.appId} not found`,
          }),
        );
        return;
      }
    } catch (error) {
      logger.error("Error in getAppById controller:", { error, requestId: (req as any).requestId });
      if (!res.headersSent) {
        res.status(500).json(
          errorResponse({
            error,
            code: "INTERNAL_ERROR",
            message: "Failed to get app by ID",
          }),
        );
      }
    }
  };
}
