import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  ApiResponse,
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  parseIncludeQuery,
  successResponse,
  errorResponse,
  IncludeQuery,
} from "@neuratalk/common";
import {
  CreateIntentUtteranceTranslationRequest,
  UpdateIntentUtteranceTranslationRequest,
} from "../schemas";
import { logger } from "@neuratalk/common";
import { WhereOptions } from "sequelize";
import { AppContext } from "../types/context.types";

export class IntentUtteranceTranslationController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/intent-utterance-translations:
   *   post:
   *     summary: Create a new intent utterance translation
   *     tags: [Intent Utterance Translations]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/IntentUtteranceTranslation'
   *     responses:
   *       201:
   *         description: Intent utterance translation created successfully
   */
  public create = async (
    req: Request<any, any, CreateIntentUtteranceTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const intentUtteranceTranslation = await this.models.IntentUtteranceTranslation.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`Intent utterance translation created: ${intentUtteranceTranslation.id}`);
      res.status(201).json(successResponse(intentUtteranceTranslation));
    } catch (error) {
      logger.error("Error creating intent utterance translation:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterance-translations:
   *   get:
   *     summary: Get all intent utterance translations
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: query
   *         name: page
   *         schema: { type: integer }
   *       - in: query
   *         name: limit
   *         schema: { type: integer }
   *       - in: query
   *         name: utteranceId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: langId
   *         schema: { type: string, format: uuid }
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., utterance,language)
   *     responses:
   *       200:
   *         description: List of intent utterance translations
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const result = await getPaginatedResults(
        this.models.IntentUtteranceTranslation,
        req.query,
        [],
        includeAssociations,
      );

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching intent utterance translations:", error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent utterance translations",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterance-translations/{id}:
   *   get:
   *     summary: Get intent utterance translation by ID
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., utterance,language)
   *     responses:
   *       200:
   *         description: Intent utterance translation object
   */
  public getById = async (
    req: Request<UuidParams, any, any, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const intentUtteranceTranslation = await this.models.IntentUtteranceTranslation.findOne({
        where: { id } as WhereOptions,
        include: includeAssociations,
      });

      if (!intentUtteranceTranslation) {
        res
          .status(404)
          .json(
            errorResponse({ code: "NOT_FOUND", message: "Intent utterance translation not found" }),
          );
        return;
      }

      res.json(successResponse(intentUtteranceTranslation));
    } catch (error) {
      logger.error(`Error fetching intent utterance translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to fetch intent utterance translation",
        }),
      );
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterance-translations/{id}:
   *   put:
   *     summary: Update an intent utterance translation
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/IntentUtteranceTranslation'
   *     responses:
   *       200:
   *         description: Intent utterance translation updated successfully
   */
  public update = async (
    req: Request<UuidParams, any, UpdateIntentUtteranceTranslationRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      const [updated] = await this.models.IntentUtteranceTranslation.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id } as WhereOptions,
        },
      );

      if (!updated) {
        res
          .status(404)
          .json(
            errorResponse({ code: "NOT_FOUND", message: "Intent utterance translation not found" }),
          );
        return;
      }

      const intentUtteranceTranslation = await this.models.IntentUtteranceTranslation.findByPk(id);
      logger.info(`Intent utterance translation updated: ${id}`);

      res.json(successResponse(intentUtteranceTranslation));
    } catch (error) {
      logger.error(`Error updating intent utterance translation ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/intent-utterance-translations/{id}:
   *   delete:
   *     summary: Delete an intent utterance translation
   *     tags: [Intent Utterance Translations]
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *     responses:
   *       204:
   *         description: Intent utterance translation deleted successfully
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.IntentUtteranceTranslation.destroy({ where: { id } });

      if (!deleted) {
        res
          .status(404)
          .json(
            errorResponse({ code: "NOT_FOUND", message: "Intent utterance translation not found" }),
          );
        return;
      }

      logger.info(`Intent utterance translation deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting intent utterance translation ${req.params.id}:`, error);
      res.status(500).json(
        errorResponse({
          error,
          code: "INTERNAL_ERROR",
          message: "Failed to delete intent utterance translation",
        }),
      );
    }
  };
}
