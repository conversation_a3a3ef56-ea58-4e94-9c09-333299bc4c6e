import { Request, Response } from "express";
import { Models } from "@neuratalk/bot-store";
import {
  getPaginatedResults,
  PaginationQuery,
  UuidParams,
  parseIncludeQuery,
  successResponse,
  errorResponse,
  IncludeQuery,
} from "@neuratalk/common";
import { CreateFaqItemRequest, UpdateFaqItemRequest } from "../schemas";
import { logger } from "@neuratalk/common";
import { AppContext } from "../types/context.types";

export class FaqItemsController {
  private models: Models;

  constructor(context: AppContext) {
    this.models = context.db.models;
  }

  /**
   * @swagger
   * /api/v1/faq-items:
   *   post:
   *     summary: Create FAQ item
   *     tags: [FAQ Items]
   *     security:
   *       - BearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateFaqItemSchema'
   *     responses:
   *       201:
   *         description: FAQ item created successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/FaqItemSchema'
   *       400:
   *         description: Validation error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public create = async (
    req: Request<any, any, CreateFaqItemRequest>,
    res: Response,
  ): Promise<void> => {
    try {
      const userId = req.user.id;
      const faqItem = await this.models.FaqItems.create({
        ...req.body,
        createdBy: userId,
        updatedBy: userId,
      });

      logger.info(`FAQ item created: ${faqItem.id}`);
      res.status(201).json(successResponse(faqItem));
    } catch (error) {
      logger.error("Error creating FAQ item:", error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items:
   *   get:
   *     summary: List FAQ items with filtering and pagination
   *     tags: [FAQ Items]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           minimum: 1
   *           default: 1
   *         description: Page number for pagination
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 20
   *         description: Number of items per page
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: Search term to filter FAQ items
   *       - in: query
   *         name: filter
   *         schema:
   *           type: string
   *         description: JSON filter object
   *         example: '{"botId":{"eq":"uuid"}}'
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., faqTranslations,category)
   *     responses:
   *       200:
   *         description: List of FAQ items with pagination
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/PaginatedFaqItemsSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public getAll = async (
    req: Request<any, any, any, PaginationQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const result = await getPaginatedResults(
        this.models.FaqItems,
        req.query,
        [],
        includeAssociations,
      );

      res.json(successResponse(result));
    } catch (error) {
      logger.error("Error fetching FAQ items:", error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch FAQ items" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   get:
   *     summary: Get FAQ item by ID
   *     tags: [FAQ Items]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: FAQ item ID
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., faqTranslations,category)
   *     responses:
   *       200:
   *         description: FAQ item retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/FaqItemSchema'
   *       404:
   *         description: FAQ item not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public getById = async (
    req: Request<UuidParams, any, any, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const faqItem = await this.models.FaqItems.findOne({
        where: { id },
        include: includeAssociations,
      });

      if (!faqItem) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "FAQ item not found" }));
        return;
      }

      res.json(successResponse(faqItem));
    } catch (error) {
      logger.error(`Error fetching FAQ item ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to fetch FAQ item" }),
        );
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   put:
   *     summary: Update FAQ item
   *     tags: [FAQ Items]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: FAQ item ID
   *       - in: query
   *         name: include
   *         schema:
   *           type: string
   *         description: Comma-separated list of associations to include (e.g., faqTranslations,category)
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateFaqItemSchema'
   *     responses:
   *       200:
   *         description: FAQ item updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               allOf:
   *                 - $ref: '#/components/schemas/ApiResponseSchema'
   *                 - type: object
   *                   properties:
   *                     data:
   *                       $ref: '#/components/schemas/FaqItemSchema'
   *       400:
   *         description: Validation error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ValidationErrorResponseSchema'
   *       404:
   *         description: FAQ item not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public update = async (
    req: Request<UuidParams, any, UpdateFaqItemRequest, IncludeQuery>,
    res: Response,
  ): Promise<void> => {
    try {
      const { id } = req.params;
      const userId = req.user?.id || "system";

      const [updated] = await this.models.FaqItems.update(
        {
          ...req.body,
          updatedBy: userId,
        },
        {
          where: { id },
        },
      );

      if (!updated) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "FAQ item not found" }));
        return;
      }

      const includeAssociations = parseIncludeQuery(req.query.include, this.models);

      const faqItem = await this.models.FaqItems.findByPk(id, {
        include: includeAssociations,
      });
      logger.info(`FAQ item updated: ${id}`);

      res.json(successResponse(faqItem));
    } catch (error) {
      logger.error(`Error updating FAQ item ${req.params.id}:`, error);
      res.status(400).json(errorResponse({ error, code: "VALIDATION_ERROR" }));
    }
  };

  /**
   * @swagger
   * /api/v1/faq-items/{id}:
   *   delete:
   *     summary: Delete FAQ item
   *     tags: [FAQ Items]
   *     security:
   *       - BearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: FAQ item ID
   *     responses:
   *       204:
   *         description: FAQ item deleted successfully
   *       404:
   *         description: FAQ item not found
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/NotFoundErrorResponseSchema'
   *       500:
   *         description: Internal server error
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/InternalServerErrorResponseSchema'
   */
  public delete = async (req: Request<UuidParams>, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const deleted = await this.models.FaqItems.destroy({ where: { id } });

      if (!deleted) {
        res.status(404).json(errorResponse({ code: "NOT_FOUND", message: "FAQ item not found" }));
        return;
      }

      logger.info(`FAQ item deleted: ${id}`);
      res.status(204).send();
    } catch (error) {
      logger.error(`Error deleting FAQ item ${req.params.id}:`, error);
      res
        .status(500)
        .json(
          errorResponse({ error, code: "INTERNAL_ERROR", message: "Failed to delete FAQ item" }),
        );
    }
  };
}
