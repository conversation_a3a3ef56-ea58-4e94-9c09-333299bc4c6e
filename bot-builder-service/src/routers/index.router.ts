import { Router } from "express";
import { AppContext } from "../types/context.types";
import { createKnowledgeRoutes } from "./knowledge.routes";
import { createBotRoutes } from "./bot.routes";
import { createFlowRoutes } from "./flow.routes";
import { createLanguageRoutes } from "./language.routes";

export function createRoutes(context: AppContext): Router[] {
  const knowledgeRouter = createKnowledgeRoutes(context);
  const botRouter = createBotRoutes(context);
  const flowRouter = createFlowRoutes(context);
  const languageRouter = createLanguageRoutes(context);

  return [knowledgeRouter, botRouter, flowRouter, languageRouter];
}
