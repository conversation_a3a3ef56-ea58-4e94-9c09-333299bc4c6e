import { z } from "zod";

/**
 * Common API Response Schemas
 *
 * These schemas define the structure of API responses across all endpoints.
 */

// Base API Response Schema
export const ApiResponseSchema = z
  .object({
    success: z.boolean(),
    data: z.any().optional(),
    error: z
      .object({
        code: z.string(),
        message: z.string(),
        details: z.record(z.any()).optional(),
        stack: z.string().optional(),
      })
      .optional(),
    timestamp: z.string().datetime(),
    requestId: z.string().optional(),
  })
  .strict();

// Error Response Schema
export const ErrorResponseSchema = z
  .object({
    success: z.literal(false),
    error: z.object({
      code: z.string(),
      message: z.string(),
      details: z.record(z.any()).optional(),
      stack: z.string().optional(),
    }),
    timestamp: z.string().datetime(),
    requestId: z.string().optional(),
  })
  .strict();

// Success Response Schema (generic)
export const SuccessResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z
    .object({
      success: z.literal(true),
      data: dataSchema,
      timestamp: z.string().datetime(),
      requestId: z.string().optional(),
    })
    .strict();

// Pagination Schema
export const PaginationSchema = z
  .object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  })
  .strict();

// Paginated Response Schema (generic)
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z
    .object({
      items: z.array(itemSchema),
      pagination: PaginationSchema,
    })
    .strict();

// Common HTTP Status Response Schemas
export const CreatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  SuccessResponseSchema(dataSchema);

export const UpdatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  SuccessResponseSchema(dataSchema);

export const DeletedResponseSchema = z
  .object({
    success: z.literal(true),
    timestamp: z.string().datetime(),
    requestId: z.string().optional(),
  })
  .strict();

// Validation Error Response Schema
export const ValidationErrorResponseSchema = z
  .object({
    success: z.literal(false),
    error: z.object({
      code: z.literal("VALIDATION_ERROR"),
      message: z.string(),
      details: z.record(z.any()),
    }),
    timestamp: z.string().datetime(),
    requestId: z.string().optional(),
  })
  .strict();

// Not Found Error Response Schema
export const NotFoundErrorResponseSchema = z
  .object({
    success: z.literal(false),
    error: z.object({
      code: z.literal("NOT_FOUND"),
      message: z.string(),
    }),
    timestamp: z.string().datetime(),
    requestId: z.string().optional(),
  })
  .strict();

// Internal Server Error Response Schema
export const InternalServerErrorResponseSchema = z
  .object({
    success: z.literal(false),
    error: z.object({
      code: z.literal("INTERNAL_ERROR"),
      message: z.string(),
      stack: z.string().optional(),
    }),
    timestamp: z.string().datetime(),
    requestId: z.string().optional(),
  })
  .strict();

// Type extraction
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T };
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type SuccessResponse<T> = {
  success: true;
  data: T;
  timestamp: Date;
  requestId?: string;
};
export type PaginatedResponse<T> = {
  items: T[];
  pagination: z.infer<typeof PaginationSchema>;
};
export type CreatedResponse<T> = SuccessResponse<T>;
export type UpdatedResponse<T> = SuccessResponse<T>;
export type DeletedResponse = z.infer<typeof DeletedResponseSchema>;
export type ValidationErrorResponse = z.infer<typeof ValidationErrorResponseSchema>;
export type NotFoundErrorResponse = z.infer<typeof NotFoundErrorResponseSchema>;
export type InternalServerErrorResponse = z.infer<typeof InternalServerErrorResponseSchema>;
