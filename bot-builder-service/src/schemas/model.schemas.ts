import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

/**
 * Model Schemas
 * 
 * These schemas mirror the Sequelize model attributes from bot-store package.
 * They provide a centralized place for model structure definitions that can be
 * used for both validation and OpenAPI documentation generation.
 */

// Base audit fields that are common across most models
const BaseAuditSchema = z.object({
  id: UuidSchema,
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  deletedAt: z.string().datetime().optional(),
  createdBy: UuidSchema,
  updatedBy: UuidSchema,
  deletedBy: UuidSchema.optional(),
}).strict();

// Language Model Schema (mirrors LanguageAttributes)
export const LanguageSchema = z.object({
  id: UuidSchema,
  name: z.string(),
  code: z.string(),
  createdAt: z.string().datetime(),
}).strict();

// Bot Language Model Schema (mirrors BotLanguageAttributes)
export const BotLanguageSchema = z.object({
  ...BaseAuditSchema.shape,
  botId: UuidSchema,
  langId: UuidSchema,
  // Optional associations
  bot: z.any().optional(),
  language: LanguageSchema.optional(),
}).strict();

// Entity Model Schema (mirrors EntitiesAttributes)
export const EntitySchema = z.object({
  ...BaseAuditSchema.shape,
  name: z.string(),
  botId: UuidSchema,
  intentId: UuidSchema,
  metadata: z.record(z.any()).optional(),
  // Optional associations
  bot: z.any().optional(),
  intentItem: z.any().optional(),
}).strict();

// FAQ Category Model Schema (mirrors FaqCategoryAttributes)
export const FaqCategorySchema = z.object({
  ...BaseAuditSchema.shape,
  botId: UuidSchema,
  name: z.string(),
  description: z.string().optional(),
  // Optional associations
  bot: z.any().optional(),
}).strict();

// FAQ Item Model Schema (mirrors FaqItemsAttributes)
export const FaqItemSchema = z.object({
  ...BaseAuditSchema.shape,
  botId: UuidSchema,
  flowId: UuidSchema.optional(),
  categoryId: UuidSchema,
  // Optional associations
  bot: z.any().optional(),
  flow: z.any().optional(),
  category: FaqCategorySchema.optional(),
}).strict();

// FAQ Translation Model Schema (mirrors FaqTranslationAttributes)
export const FaqTranslationSchema = z.object({
  ...BaseAuditSchema.shape,
  faqId: UuidSchema,
  langId: UuidSchema,
  questions: z.array(z.string()),
  answer: z.string(),
  metadata: z.record(z.any()).optional(),
  // Optional associations
  faqItem: FaqItemSchema.optional(),
  language: LanguageSchema.optional(),
}).strict();

// Intent Item Model Schema (mirrors IntentItemsAttributes)
export const IntentItemSchema = z.object({
  ...BaseAuditSchema.shape,
  botId: UuidSchema,
  flowId: UuidSchema.optional(),
  name: z.string(),
  description: z.string().optional(),
  // Optional associations
  bot: z.any().optional(),
  flow: z.any().optional(),
}).strict();

// Intent Utterance Model Schema (mirrors IntentUtteranceAttributes)
export const IntentUtteranceSchema = z.object({
  ...BaseAuditSchema.shape,
  intentId: UuidSchema,
  metadata: z.record(z.any()).optional(),
  // Optional associations
  intentItem: IntentItemSchema.optional(),
}).strict();

// Intent Utterance Translation Model Schema (mirrors IntentUtteranceTranslationAttributes)
export const IntentUtteranceTranslationSchema = z.object({
  ...BaseAuditSchema.shape,
  utteranceId: UuidSchema,
  langId: UuidSchema,
  text: z.string(),
  entities: z.record(z.any()).optional(),
  // Optional associations
  utterance: IntentUtteranceSchema.optional(),
  language: LanguageSchema.optional(),
}).strict();

// Generic pagination schema
export const PaginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  total: z.number(),
  totalPages: z.number(),
  hasNext: z.boolean(),
  hasPrev: z.boolean(),
}).strict();

// Generic paginated response creator
export const createPaginatedSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    items: z.array(itemSchema),
    pagination: PaginationSchema,
  }).strict();

// Paginated response schemas for each model
export const PaginatedLanguagesSchema = createPaginatedSchema(LanguageSchema);
export const PaginatedBotLanguagesSchema = createPaginatedSchema(BotLanguageSchema);
export const PaginatedEntitiesSchema = createPaginatedSchema(EntitySchema);
export const PaginatedFaqCategoriesSchema = createPaginatedSchema(FaqCategorySchema);
export const PaginatedFaqItemsSchema = createPaginatedSchema(FaqItemSchema);
export const PaginatedFaqTranslationsSchema = createPaginatedSchema(FaqTranslationSchema);
export const PaginatedIntentItemsSchema = createPaginatedSchema(IntentItemSchema);
export const PaginatedIntentUtterancesSchema = createPaginatedSchema(IntentUtteranceSchema);
export const PaginatedIntentUtteranceTranslationsSchema = createPaginatedSchema(IntentUtteranceTranslationSchema);

// Type exports for TypeScript usage
export type Language = z.infer<typeof LanguageSchema>;
export type BotLanguage = z.infer<typeof BotLanguageSchema>;
export type Entity = z.infer<typeof EntitySchema>;
export type FaqCategory = z.infer<typeof FaqCategorySchema>;
export type FaqItem = z.infer<typeof FaqItemSchema>;
export type FaqTranslation = z.infer<typeof FaqTranslationSchema>;
export type IntentItem = z.infer<typeof IntentItemSchema>;
export type IntentUtterance = z.infer<typeof IntentUtteranceSchema>;
export type IntentUtteranceTranslation = z.infer<typeof IntentUtteranceTranslationSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;

// Paginated response types
export type PaginatedLanguages = z.infer<typeof PaginatedLanguagesSchema>;
export type PaginatedBotLanguages = z.infer<typeof PaginatedBotLanguagesSchema>;
export type PaginatedEntities = z.infer<typeof PaginatedEntitiesSchema>;
export type PaginatedFaqCategories = z.infer<typeof PaginatedFaqCategoriesSchema>;
export type PaginatedFaqItems = z.infer<typeof PaginatedFaqItemsSchema>;
export type PaginatedFaqTranslations = z.infer<typeof PaginatedFaqTranslationsSchema>;
export type PaginatedIntentItems = z.infer<typeof PaginatedIntentItemsSchema>;
export type PaginatedIntentUtterances = z.infer<typeof PaginatedIntentUtterancesSchema>;
export type PaginatedIntentUtteranceTranslations = z.infer<typeof PaginatedIntentUtteranceTranslationsSchema>;
