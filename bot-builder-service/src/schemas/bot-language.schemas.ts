import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";

// Language schemas
export const CreateLanguageSchema = z
  .object({
    name: z.string().min(1).max(255),
    code: z.string().min(1).max(10),
  })
  .strict();

export const UpdateLanguageSchema = CreateLanguageSchema.partial().strict();

// Bot Language schemas
export const CreateBotLanguageSchema = z
  .object({
    botId: UuidSchema,
    langId: UuidSchema,
  })
  .strict();

export const UpdateBotLanguageSchema = CreateBotLanguageSchema.partial().strict();

// Response schemas for languages
export const LanguageResponseSchema = z
  .object({
    id: UuidSchema,
    name: z.string(),
    code: z.string(),
    createdAt: z.string().datetime(),
  })
  .strict();

export const LanguageListResponseSchema = z
  .object({
    items: z.array(LanguageResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Response schemas for bot languages
export const BotLanguageResponseSchema = z
  .object({
    id: UuidSchema,
    botId: UuidSchema,
    langId: UuidSchema,
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
    deletedAt: z.string().datetime().optional(),
    createdBy: UuidSchema,
    updatedBy: UuidSchema,
    deletedBy: UuidSchema.optional(),
    // Optional associations
    bot: z.any().optional(),
    language: z.any().optional(),
  })
  .strict();

export const BotLanguageListResponseSchema = z
  .object({
    items: z.array(BotLanguageResponseSchema),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  })
  .strict();

// Type extraction
export type CreateLanguageRequest = z.infer<typeof CreateLanguageSchema>;
export type UpdateLanguageRequest = z.infer<typeof UpdateLanguageSchema>;
export type CreateBotLanguageRequest = z.infer<typeof CreateBotLanguageSchema>;
export type UpdateBotLanguageRequest = z.infer<typeof UpdateBotLanguageSchema>;

// Response type extraction
export type LanguageResponse = z.infer<typeof LanguageResponseSchema>;
export type LanguageListResponse = z.infer<typeof LanguageListResponseSchema>;
export type BotLanguageResponse = z.infer<typeof BotLanguageResponseSchema>;
export type BotLanguageListResponse = z.infer<typeof BotLanguageListResponseSchema>;
