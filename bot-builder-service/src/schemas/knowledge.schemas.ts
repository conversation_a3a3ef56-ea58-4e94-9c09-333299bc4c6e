import { z } from "zod";
import { UuidSchema } from "@neuratalk/common";



// FAQ Category schemas
export const CreateFaqCategorySchema = z.object({
  botId: UuidSchema,
  name: z.string().min(1).max(255),
  description: z.string().optional(),
}).strict();

export const UpdateFaqCategorySchema = CreateFaqCategorySchema.partial().strict();

// FAQ Item schemas
export const CreateFaqItemSchema = z.object({
  botId: UuidSchema,
  flowId: UuidSchema.optional(),
  categoryId: UuidSchema,
}).strict();

export const UpdateFaqItemSchema = CreateFaqItemSchema.partial().strict();

// FAQ Translation schemas
export const CreateFaqTranslationSchema = z.object({
  faqId: UuidSchema,
  langId: UuidSchema,
  questions: z.array(z.string().min(1)).min(1),
  answer: z.string().min(1),
  metadata: z.record(z.any()).optional(),
}).strict();

export const UpdateFaqTranslationSchema = CreateFaqTranslationSchema.partial().strict();

// Intent Item schemas
export const CreateIntentItemSchema = z.object({
  botId: UuidSchema,
  flowId: UuidSchema.optional(),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
}).strict();

export const UpdateIntentItemSchema = CreateIntentItemSchema.partial().strict();

// Intent Utterance schemas
export const CreateIntentUtteranceSchema = z.object({
  intentId: UuidSchema,
  metadata: z.record(z.any()).optional(),
}).strict();

export const UpdateIntentUtteranceSchema = CreateIntentUtteranceSchema.partial().strict();

// Intent Utterance Translation schemas
export const CreateIntentUtteranceTranslationSchema = z.object({
  utteranceId: UuidSchema,
  langId: UuidSchema,
  text: z.string().min(1),
  entities: z.record(z.any()).optional(), // Or a more specific type for entities
}).strict();

export const UpdateIntentUtteranceTranslationSchema = CreateIntentUtteranceTranslationSchema.partial().strict();

// Entity schemas
export const CreateEntitySchema = z.object({
  botId: UuidSchema,
  intentId: UuidSchema,
  name: z.string().min(1).max(100),
  metadata: z.record(z.any()).optional(),
}).strict();

export const UpdateEntitySchema = CreateEntitySchema.partial().strict();

// Type extraction
export type CreateFaqCategoryRequest = z.infer<typeof CreateFaqCategorySchema>;
export type UpdateFaqCategoryRequest = z.infer<typeof UpdateFaqCategorySchema>;
export type CreateFaqItemRequest = z.infer<typeof CreateFaqItemSchema>;
export type UpdateFaqItemRequest = z.infer<typeof UpdateFaqItemSchema>;
export type CreateFaqTranslationRequest = z.infer<typeof CreateFaqTranslationSchema>;
export type UpdateFaqTranslationRequest = z.infer<typeof UpdateFaqTranslationSchema>;
export type CreateIntentItemRequest = z.infer<typeof CreateIntentItemSchema>;
export type UpdateIntentItemRequest = z.infer<typeof UpdateIntentItemSchema>;
export type CreateIntentUtteranceRequest = z.infer<typeof CreateIntentUtteranceSchema>;
export type UpdateIntentUtteranceRequest = z.infer<typeof UpdateIntentUtteranceSchema>;
export type CreateIntentUtteranceTranslationRequest = z.infer<typeof CreateIntentUtteranceTranslationSchema>;
export type UpdateIntentUtteranceTranslationRequest = z.infer<typeof UpdateIntentUtteranceTranslationSchema>;
export type CreateEntityRequest = z.infer<typeof CreateEntitySchema>;
export type UpdateEntityRequest = z.infer<typeof UpdateEntitySchema>;
